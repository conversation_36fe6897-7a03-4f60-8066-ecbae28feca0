import fetch from 'node-fetch';

async function getSpotifyToken(clientId: string, clientSecret: string): Promise<string> {
  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
    },
    body: 'grant_type=client_credentials',
  });
  const data = await response.json();
  return data.access_token;
}

async function getLatestRelease(artistId: string, clientId: string, clientSecret: string): Promise<any> {
  const token = await getSpotifyToken(clientId, clientSecret);
  const response = await fetch(`https://api.spotify.com/v1/artists/${artistId}/albums?limit=20&include_groups=single,appears_on`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  const data = await response.json();
  return data.items[0];
}

// Example usage
const clientId = 'f5e6e930e8814373b5febc0f76b87d2e';
const clientSecret = '04300cb7ea9440bc90c9beb0d8e91dcf';
const artistId = '36VxME9IxYM3mGnnNCkHrQ'; // e.g., '1Xyo4u8uXC1ZmMpatF05PJ' for The Weeknd

getLatestRelease(artistId, clientId, clientSecret)
  .then(release => console.log('Latest Release:', release, release.release_date))
  .catch(error => console.error('Error:', error));